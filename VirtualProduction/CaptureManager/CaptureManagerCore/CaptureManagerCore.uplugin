{"FileVersion": 1, "Version": 1, "VersionName": "1.0.0", "FriendlyName": "Capture Manager Core", "Description": "The Capture Manager Core plugin contains utility modules that are shared between Capture Manager App plugin and Capture Manager Editor plugin.", "Category": "Virtual Production", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "http://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": true, "EnabledByDefault": false, "IsBetaVersion": false, "IsExperimentalVersion": false, "Installed": false, "Hidden": true, "Modules": [{"Name": "CaptureUtils", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "CaptureProtocolStack", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "DataIngestCore", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "LiveLinkHubCaptureMessaging", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "CaptureManagerStyle", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "CaptureManagerTakeMetadata", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "CaptureData", "Enabled": true}]}