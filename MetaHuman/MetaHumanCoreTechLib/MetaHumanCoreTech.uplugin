{"FileVersion": 3, "Version": 1, "VersionName": "1.0.0", "FriendlyName": "MetaHuman Core Tech", "Description": "The core technology behind the MetaHuman Creator and MetaHuman Animator plugins.", "Category": "<PERSON><PERSON><PERSON><PERSON>", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "http://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": true, "IsBetaVersion": false, "IsExperimentalVersion": false, "Installed": false, "EnabledByDefault": false, "SupportedTargetPlatforms": ["Win64", "Linux"], "Modules": [{"Name": "MetaHumanCoreTechLib", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanCoreTech", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "MetaHumanImageViewer", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanCaptureData", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanPipelineCore", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}], "Plugins": [{"Name": "MeshModelingToolset", "Enabled": true}, {"Name": "EditorScriptingUtilities", "Enabled": true}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Enabled": true}, {"Name": "MetaHumanSDK", "Enabled": true}, {"Name": "CaptureData", "Enabled": true}], "LocalizationTargets": [{"Name": "MetaHumanCoreTech", "LoadingPolicy": "Editor", "ConfigGenerationPolicy": "Auto"}]}