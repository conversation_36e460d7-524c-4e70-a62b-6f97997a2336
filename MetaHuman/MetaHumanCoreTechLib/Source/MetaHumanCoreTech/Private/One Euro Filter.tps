<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>One Euro Filter</Name>
  <!-- Software Name and Version  -->
<!-- Software Name: One Euro Filter
    Download Link: https://github.com/casiez/OneEuroFilter/tree/main/.github
    Version: N/A
    Notes: C++
        -->
<Location>Engine/Plugins/MetaHuman/MetaHumanCoreTechLib/Source/MetaHumanCoreTech/Private</Location>
<Function>Smoothing animation</Function>
<Eula>https://github.com/casiez/OneEuroFilter/blob/main/python/LICENSE</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensee</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>