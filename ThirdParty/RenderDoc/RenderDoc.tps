<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>RenderDoc</Name>
  <Location>/Engine/Source/ThirdParty/RenderDoc/</Location>
  <Date>2017-02-14T16:34:51.030656-07:00</Date>
  <Function>Graphics debugger, visualizer, and profiler</Function>
  <Justification>Provides an API layer to the RenderDoc dlls</Justification>
  <Eula>https://github.com/baldurk/renderdoc/blob/master/LICENSE.md</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <Notification />
  <LicenseFolder>None</LicenseFolder>
</TpsData>