// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "Dataflow/DataflowContent.h"
#include "Dataflow/DataflowEditorPreviewSceneBase.h"
#include "Dataflow/DataflowNodeParameters.h"
#include "DynamicMesh/DynamicMeshAABBTree3.h"

class UDataflowEditor;
class USkeletalMeshComponent;
class UDynamicMeshComponent;
class UMeshElementsVisualizer;
class UDataflowEdNode;
class UInteractiveToolPropertySet;

namespace UE
{
	namespace Geometry
	{
		class FDynamicMesh3;
	}
}

/**
 * Dataflow construction scene holding all the dynamic meshes used/displayed in the dataflow graph
 */
class DATAFLOWEDITOR_API FDataflowConstructionScene : public FDataflowPreviewSceneBase
{
	/*(const void* ObjectPointer, const int32 ObjectIndex)*/
	typedef TPair<TObjectPtr<const UDataflowEdNode>, int32> FDataflowRenderKey;

	struct FDebugMesh
	{
		UE::Geometry::FDynamicMesh3 ResultMesh;
		UE::Geometry::FDynamicMeshAABBTree3 Spatial;
		TMap<int32, int32> VertexMap;
		TMap<int32, int32> FaceMap;

		void Reset();
		void Build(const TArray<TObjectPtr<UDynamicMeshComponent>>& InDynamicMeshComponents);
	};

public:

	FDataflowConstructionScene(FPreviewScene::ConstructionValues ConstructionValues, UDataflowEditor* Editor);
	virtual ~FDataflowConstructionScene();

	// FGCObject interface
	virtual void AddReferencedObjects(FReferenceCollector& Collector) override;
	
	/** Tick data flow scene */
	virtual void TickDataflowScene(const float DeltaSeconds) override;

	/** Check if the preview scene can run simulation */
	virtual bool CanRunSimulation() const override {return false;}
	
	/** Is there ANYTHING to render */
	bool HasRenderableGeometry();

	/** Close the construction scene */
	void ResetConstructionScene();

	/** Update the construction scene */
	void UpdateConstructionScene();
	
	/** Hide all or a single component */
	void SetVisibility(bool bVisible, UActorComponent* Component = nullptr);

	/** Array of all DynamicMeshComponents in the scene */
	TArray<TObjectPtr<UDynamicMeshComponent>> GetDynamicMeshComponents() const;

	FDebugMesh DebugMesh;

private:

	/** Reset the scene mesh visualizer */
	void ResetWireframeMeshElementsVisualizer();

	/** Update the scene mesh visualizer */
	void UpdateWireframeMeshElementsVisualizer();

	/** Add a mesh visualizer to the scene */
	void AddWireframeMeshElementsVisualizer();

	/** Reset all the scene components (dynamic mesh / primitive)*/
    void ResetSceneComponents();
	
	/** Build the dynamic mesh components from the dataflow graph render targets*/
	void UpdateDynamicMeshComponents();

	/** Build the primitive components from the dataflow graph selected node */
    void UpdatePrimitiveComponents();
	
	/** Add a dynamic mesh component to the scene */
	TObjectPtr<UDynamicMeshComponent>& AddDynamicMeshComponent(
		FDataflowRenderKey InKey, const FString& MeshName, UE::Geometry::FDynamicMesh3&& DynamicMesh,  const TArray<UMaterialInterface*>& MaterialSet);

	/** Remove the scene component from selection and destroy it */
	void RemoveSceneComponent(USelection* SelectedComponents, UPrimitiveComponent* PrimitiveComponent);
	
	/** Dynamic mesh components generated by the tools for edit/manipulation*/
	typedef TPair<FDataflowRenderKey, TObjectPtr<UDynamicMeshComponent>> FRenderElement;
	TMap<FDataflowRenderKey, TObjectPtr<UDynamicMeshComponent>> DynamicMeshComponents;

	/** Rest space wireframe. They have to get ticked to be able to respond to setting changes. [ticked] */
	typedef TPair<TObjectPtr<UDynamicMeshComponent>, TObjectPtr<UMeshElementsVisualizer>> FRenderWireElement;
	TMap<TObjectPtr<UDynamicMeshComponent>,TObjectPtr<UMeshElementsVisualizer> > WireframeElements;

	/** Source meshes for Wireframe rendering */
	TArray<UDynamicMeshComponent*> MeshComponentsForWireframeRendering;

	/** Property objects (visible or not) that get ticked. [ticked] */
	TArray<TObjectPtr<UInteractiveToolPropertySet>> PropertyObjectsToTick;

	/** Last Rendered Timestamp */
	UE::Dataflow::FTimestamp LastRenderedTimestamp = UE::Dataflow::FTimestamp::Invalid;

	/** Show the wireframe on render */ 
	bool bConstructionViewWireframe = true;
	
	/** Primitive components generated by the nodes and to be used by the tools */
	TArray<TObjectPtr<UPrimitiveComponent>> PrimitiveComponents;
};
