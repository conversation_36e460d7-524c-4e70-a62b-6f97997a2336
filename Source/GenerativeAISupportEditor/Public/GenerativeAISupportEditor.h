// Copyright (c) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
// Licensed under the MIT License. See LICENSE file in the root directory of this
// source tree or http://opensource.org/licenses/MIT.


#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "Editor/GenEditorWindow.h"
#include "Framework/Commands/UICommandList.h"

#define LOCTEXT_NAMESPACE "FGenerativeAISupportEditorModule"

// Define the tab ID for our editor window
static const FName GenEditorTabId("GenEditorWindow");

/**
 * 
 */
class GENERATIVEAISUPPORTEDITOR_API FGenerativeAISupportEditorModule : public IModuleInterface
{
public:
	/** Constructor */
	FGenerativeAISupportEditorModule();

	/** IModuleInterface implementation */
	virtual void StartupModule() override;
	virtual void ShutdownModule() override;

private:
	void RegisterSettings();
	void UnregisterSettings();
	bool HandleSettingsSaved();
	void RegisterMenuExtension();
	void UnregisterMenuExtension();
	void OnEditorWindowMenuClicked();
	void InitializePythonIntegration();

	bool bSettingsRegistered;
	TSharedPtr<FUICommandList> PluginCommands;
};

#undef LOCTEXT_NAMESPACE