<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Code snippet from "Efficient computation of noise in GLSL" article</Name>
  <Location>/Engine/Shaders/Private/Random.usf</Location>
  <Date>2016-09-09T11:14:02.8298717-04:00</Date>
  <Function>Fast computation for a variant of Perlin noise</Function>
  <Justification>Faster than existing Perlin noise, more efficient for noise derivatives</Justification>
  <Eula>https://github.com/ashima/webgl-noise/blob/master/LICENSE</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>None</LicenseFolder>
</TpsData>