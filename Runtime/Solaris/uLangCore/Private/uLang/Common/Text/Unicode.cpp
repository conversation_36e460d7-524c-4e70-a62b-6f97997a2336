// Copyright Epic Games, Inc. All Rights Reserved.

#include "uLang/Common/Text/Unicode.h"

namespace
{

/// Finds the index of the largest element <= Value using binary search
template<typename T>
inline int32_t FindLowerBound(const T * SortedTable, int32_t TableSize, uint32_t Value)
{
    int32_t Lo = 0;
    int32_t Hi = TableSize;
    while (true)
    {
        int32_t Mid = (Lo + Hi) / 2;
        if (SortedTable[Mid] <= Value)
        {
            if (Mid == Lo) break;
            Lo = Mid;
        }
        else
        {
            Hi = Mid;
            if (Mid == Lo) break;
        }
    }
    return Hi - 1; // This can become -1 which means there is no lower bound in the table
}

/*
 * Lookup tables are generated by a small Python 3 script, GenerateUnicodeLookup.py
 * Each table lists all code points that are either
 * 1. the first match in a sequence of matches
 * 2. the first non-match in a sequence of non-matches
 * If a code point is a match can then be easily determined by finding the lower bound of a given code point in the table
 * Iff the index of such lower bound is even, the code point is a match
 *
 * IdentifierStartCategories = [ 'Lu', 'Ll', 'Lt', 'Lo', 'Lm', 'Nl' ]
 * IdentifierTailCategories = [ 'Lu', 'Ll', 'Lt', 'Lo', 'Lm', 'Nl', 'Mn', 'Mc', 'Nd', 'Pc' ] plus code points 0x200c and 0x200d
 */

const uint16_t UnicodeIdentifierStartLookup16[] =
{
    0x00aa,0x00ab,0x00b5,0x00b6,0x00ba,0x00bb,0x00c0,0x00d7,0x00d8,0x00f7,0x00f8,0x02c2,0x02c6,0x02d2,0x02e0,0x02e5,0x02ec,0x02ed,0x02ee,0x02ef,0x0370,0x0375,0x0376,0x0378,
    0x037a,0x037e,0x037f,0x0380,0x0386,0x0387,0x0388,0x038b,0x038c,0x038d,0x038e,0x03a2,0x03a3,0x03f6,0x03f7,0x0482,0x048a,0x0530,0x0531,0x0557,0x0559,0x055a,0x0560,0x0589,
    0x05d0,0x05eb,0x05ef,0x05f3,0x0620,0x064b,0x066e,0x0670,0x0671,0x06d4,0x06d5,0x06d6,0x06e5,0x06e7,0x06ee,0x06f0,0x06fa,0x06fd,0x06ff,0x0700,0x0710,0x0711,0x0712,0x0730,
    0x074d,0x07a6,0x07b1,0x07b2,0x07ca,0x07eb,0x07f4,0x07f6,0x07fa,0x07fb,0x0800,0x0816,0x081a,0x081b,0x0824,0x0825,0x0828,0x0829,0x0840,0x0859,0x0860,0x086b,0x08a0,0x08b5,
    0x08b6,0x08be,0x0904,0x093a,0x093d,0x093e,0x0950,0x0951,0x0958,0x0962,0x0971,0x0981,0x0985,0x098d,0x098f,0x0991,0x0993,0x09a9,0x09aa,0x09b1,0x09b2,0x09b3,0x09b6,0x09ba,
    0x09bd,0x09be,0x09ce,0x09cf,0x09dc,0x09de,0x09df,0x09e2,0x09f0,0x09f2,0x09fc,0x09fd,0x0a05,0x0a0b,0x0a0f,0x0a11,0x0a13,0x0a29,0x0a2a,0x0a31,0x0a32,0x0a34,0x0a35,0x0a37,
    0x0a38,0x0a3a,0x0a59,0x0a5d,0x0a5e,0x0a5f,0x0a72,0x0a75,0x0a85,0x0a8e,0x0a8f,0x0a92,0x0a93,0x0aa9,0x0aaa,0x0ab1,0x0ab2,0x0ab4,0x0ab5,0x0aba,0x0abd,0x0abe,0x0ad0,0x0ad1,
    0x0ae0,0x0ae2,0x0af9,0x0afa,0x0b05,0x0b0d,0x0b0f,0x0b11,0x0b13,0x0b29,0x0b2a,0x0b31,0x0b32,0x0b34,0x0b35,0x0b3a,0x0b3d,0x0b3e,0x0b5c,0x0b5e,0x0b5f,0x0b62,0x0b71,0x0b72,
    0x0b83,0x0b84,0x0b85,0x0b8b,0x0b8e,0x0b91,0x0b92,0x0b96,0x0b99,0x0b9b,0x0b9c,0x0b9d,0x0b9e,0x0ba0,0x0ba3,0x0ba5,0x0ba8,0x0bab,0x0bae,0x0bba,0x0bd0,0x0bd1,0x0c05,0x0c0d,
    0x0c0e,0x0c11,0x0c12,0x0c29,0x0c2a,0x0c3a,0x0c3d,0x0c3e,0x0c58,0x0c5b,0x0c60,0x0c62,0x0c80,0x0c81,0x0c85,0x0c8d,0x0c8e,0x0c91,0x0c92,0x0ca9,0x0caa,0x0cb4,0x0cb5,0x0cba,
    0x0cbd,0x0cbe,0x0cde,0x0cdf,0x0ce0,0x0ce2,0x0cf1,0x0cf3,0x0d05,0x0d0d,0x0d0e,0x0d11,0x0d12,0x0d3b,0x0d3d,0x0d3e,0x0d4e,0x0d4f,0x0d54,0x0d57,0x0d5f,0x0d62,0x0d7a,0x0d80,
    0x0d85,0x0d97,0x0d9a,0x0db2,0x0db3,0x0dbc,0x0dbd,0x0dbe,0x0dc0,0x0dc7,0x0e01,0x0e31,0x0e32,0x0e34,0x0e40,0x0e47,0x0e81,0x0e83,0x0e84,0x0e85,0x0e87,0x0e89,0x0e8a,0x0e8b,
    0x0e8d,0x0e8e,0x0e94,0x0e98,0x0e99,0x0ea0,0x0ea1,0x0ea4,0x0ea5,0x0ea6,0x0ea7,0x0ea8,0x0eaa,0x0eac,0x0ead,0x0eb1,0x0eb2,0x0eb4,0x0ebd,0x0ebe,0x0ec0,0x0ec5,0x0ec6,0x0ec7,
    0x0edc,0x0ee0,0x0f00,0x0f01,0x0f40,0x0f48,0x0f49,0x0f6d,0x0f88,0x0f8d,0x1000,0x102b,0x103f,0x1040,0x1050,0x1056,0x105a,0x105e,0x1061,0x1062,0x1065,0x1067,0x106e,0x1071,
    0x1075,0x1082,0x108e,0x108f,0x10a0,0x10c6,0x10c7,0x10c8,0x10cd,0x10ce,0x10d0,0x10fb,0x10fc,0x1249,0x124a,0x124e,0x1250,0x1257,0x1258,0x1259,0x125a,0x125e,0x1260,0x1289,
    0x128a,0x128e,0x1290,0x12b1,0x12b2,0x12b6,0x12b8,0x12bf,0x12c0,0x12c1,0x12c2,0x12c6,0x12c8,0x12d7,0x12d8,0x1311,0x1312,0x1316,0x1318,0x135b,0x1380,0x1390,0x13a0,0x13f6,
    0x13f8,0x13fe,0x1401,0x166d,0x166f,0x1680,0x1681,0x169b,0x16a0,0x16eb,0x16ee,0x16f9,0x1700,0x170d,0x170e,0x1712,0x1720,0x1732,0x1740,0x1752,0x1760,0x176d,0x176e,0x1771,
    0x1780,0x17b4,0x17d7,0x17d8,0x17dc,0x17dd,0x1820,0x1879,0x1880,0x1885,0x1887,0x18a9,0x18aa,0x18ab,0x18b0,0x18f6,0x1900,0x191f,0x1950,0x196e,0x1970,0x1975,0x1980,0x19ac,
    0x19b0,0x19ca,0x1a00,0x1a17,0x1a20,0x1a55,0x1aa7,0x1aa8,0x1b05,0x1b34,0x1b45,0x1b4c,0x1b83,0x1ba1,0x1bae,0x1bb0,0x1bba,0x1be6,0x1c00,0x1c24,0x1c4d,0x1c50,0x1c5a,0x1c7e,
    0x1c80,0x1c89,0x1c90,0x1cbb,0x1cbd,0x1cc0,0x1ce9,0x1ced,0x1cee,0x1cf2,0x1cf5,0x1cf7,0x1d00,0x1dc0,0x1e00,0x1f16,0x1f18,0x1f1e,0x1f20,0x1f46,0x1f48,0x1f4e,0x1f50,0x1f58,
    0x1f59,0x1f5a,0x1f5b,0x1f5c,0x1f5d,0x1f5e,0x1f5f,0x1f7e,0x1f80,0x1fb5,0x1fb6,0x1fbd,0x1fbe,0x1fbf,0x1fc2,0x1fc5,0x1fc6,0x1fcd,0x1fd0,0x1fd4,0x1fd6,0x1fdc,0x1fe0,0x1fed,
    0x1ff2,0x1ff5,0x1ff6,0x1ffd,0x2071,0x2072,0x207f,0x2080,0x2090,0x209d,0x2102,0x2103,0x2107,0x2108,0x210a,0x2114,0x2115,0x2116,0x2119,0x211e,0x2124,0x2125,0x2126,0x2127,
    0x2128,0x2129,0x212a,0x212e,0x212f,0x213a,0x213c,0x2140,0x2145,0x214a,0x214e,0x214f,0x2160,0x2189,0x2c00,0x2c2f,0x2c30,0x2c5f,0x2c60,0x2ce5,0x2ceb,0x2cef,0x2cf2,0x2cf4,
    0x2d00,0x2d26,0x2d27,0x2d28,0x2d2d,0x2d2e,0x2d30,0x2d68,0x2d6f,0x2d70,0x2d80,0x2d97,0x2da0,0x2da7,0x2da8,0x2daf,0x2db0,0x2db7,0x2db8,0x2dbf,0x2dc0,0x2dc7,0x2dc8,0x2dcf,
    0x2dd0,0x2dd7,0x2dd8,0x2ddf,0x2e2f,0x2e30,0x3005,0x3008,0x3021,0x302a,0x3031,0x3036,0x3038,0x303d,0x3041,0x3097,0x309d,0x30a0,0x30a1,0x30fb,0x30fc,0x3100,0x3105,0x3130,
    0x3131,0x318f,0x31a0,0x31bb,0x31f0,0x3200,0x3400,0x4db6,0x4e00,0x9ff0,0xa000,0xa48d,0xa4d0,0xa4fe,0xa500,0xa60d,0xa610,0xa620,0xa62a,0xa62c,0xa640,0xa66f,0xa67f,0xa69e,
    0xa6a0,0xa6f0,0xa717,0xa720,0xa722,0xa789,0xa78b,0xa7ba,0xa7f7,0xa802,0xa803,0xa806,0xa807,0xa80b,0xa80c,0xa823,0xa840,0xa874,0xa882,0xa8b4,0xa8f2,0xa8f8,0xa8fb,0xa8fc,
    0xa8fd,0xa8ff,0xa90a,0xa926,0xa930,0xa947,0xa960,0xa97d,0xa984,0xa9b3,0xa9cf,0xa9d0,0xa9e0,0xa9e5,0xa9e6,0xa9f0,0xa9fa,0xa9ff,0xaa00,0xaa29,0xaa40,0xaa43,0xaa44,0xaa4c,
    0xaa60,0xaa77,0xaa7a,0xaa7b,0xaa7e,0xaab0,0xaab1,0xaab2,0xaab5,0xaab7,0xaab9,0xaabe,0xaac0,0xaac1,0xaac2,0xaac3,0xaadb,0xaade,0xaae0,0xaaeb,0xaaf2,0xaaf5,0xab01,0xab07,
    0xab09,0xab0f,0xab11,0xab17,0xab20,0xab27,0xab28,0xab2f,0xab30,0xab5b,0xab5c,0xab66,0xab70,0xabe3,0xac00,0xd7a4,0xd7b0,0xd7c7,0xd7cb,0xd7fc,0xf900,0xfa6e,0xfa70,0xfada,
    0xfb00,0xfb07,0xfb13,0xfb18,0xfb1d,0xfb1e,0xfb1f,0xfb29,0xfb2a,0xfb37,0xfb38,0xfb3d,0xfb3e,0xfb3f,0xfb40,0xfb42,0xfb43,0xfb45,0xfb46,0xfbb2,0xfbd3,0xfd3e,0xfd50,0xfd90,
    0xfd92,0xfdc8,0xfdf0,0xfdfc,0xfe70,0xfe75,0xfe76,0xfefd,0xff21,0xff3b,0xff41,0xff5b,0xff66,0xffbf,0xffc2,0xffc8,0xffca,0xffd0,0xffd2,0xffd8,0xffda,0xffdd,
};

const uint32_t UnicodeIdentifierStartLookup32[] =
{
    0x10000,0x1000c,0x1000d,0x10027,0x10028,0x1003b,0x1003c,0x1003e,0x1003f,0x1004e,0x10050,0x1005e,0x10080,0x100fb,0x10140,0x10175,0x10280,0x1029d,0x102a0,0x102d1,0x10300,
    0x10320,0x1032d,0x1034b,0x10350,0x10376,0x10380,0x1039e,0x103a0,0x103c4,0x103c8,0x103d0,0x103d1,0x103d6,0x10400,0x1049e,0x104b0,0x104d4,0x104d8,0x104fc,0x10500,0x10528,
    0x10530,0x10564,0x10600,0x10737,0x10740,0x10756,0x10760,0x10768,0x10800,0x10806,0x10808,0x10809,0x1080a,0x10836,0x10837,0x10839,0x1083c,0x1083d,0x1083f,0x10856,0x10860,
    0x10877,0x10880,0x1089f,0x108e0,0x108f3,0x108f4,0x108f6,0x10900,0x10916,0x10920,0x1093a,0x10980,0x109b8,0x109be,0x109c0,0x10a00,0x10a01,0x10a10,0x10a14,0x10a15,0x10a18,
    0x10a19,0x10a36,0x10a60,0x10a7d,0x10a80,0x10a9d,0x10ac0,0x10ac8,0x10ac9,0x10ae5,0x10b00,0x10b36,0x10b40,0x10b56,0x10b60,0x10b73,0x10b80,0x10b92,0x10c00,0x10c49,0x10c80,
    0x10cb3,0x10cc0,0x10cf3,0x10d00,0x10d24,0x10f00,0x10f1d,0x10f27,0x10f28,0x10f30,0x10f46,0x11003,0x11038,0x11083,0x110b0,0x110d0,0x110e9,0x11103,0x11127,0x11144,0x11145,
    0x11150,0x11173,0x11176,0x11177,0x11183,0x111b3,0x111c1,0x111c5,0x111da,0x111db,0x111dc,0x111dd,0x11200,0x11212,0x11213,0x1122c,0x11280,0x11287,0x11288,0x11289,0x1128a,
    0x1128e,0x1128f,0x1129e,0x1129f,0x112a9,0x112b0,0x112df,0x11305,0x1130d,0x1130f,0x11311,0x11313,0x11329,0x1132a,0x11331,0x11332,0x11334,0x11335,0x1133a,0x1133d,0x1133e,
    0x11350,0x11351,0x1135d,0x11362,0x11400,0x11435,0x11447,0x1144b,0x11480,0x114b0,0x114c4,0x114c6,0x114c7,0x114c8,0x11580,0x115af,0x115d8,0x115dc,0x11600,0x11630,0x11644,
    0x11645,0x11680,0x116ab,0x11700,0x1171b,0x11800,0x1182c,0x118a0,0x118e0,0x118ff,0x11900,0x11a00,0x11a01,0x11a0b,0x11a33,0x11a3a,0x11a3b,0x11a50,0x11a51,0x11a5c,0x11a84,
    0x11a86,0x11a8a,0x11a9d,0x11a9e,0x11ac0,0x11af9,0x11c00,0x11c09,0x11c0a,0x11c2f,0x11c40,0x11c41,0x11c72,0x11c90,0x11d00,0x11d07,0x11d08,0x11d0a,0x11d0b,0x11d31,0x11d46,
    0x11d47,0x11d60,0x11d66,0x11d67,0x11d69,0x11d6a,0x11d8a,0x11d98,0x11d99,0x11ee0,0x11ef3,0x12000,0x1239a,0x12400,0x1246f,0x12480,0x12544,0x13000,0x1342f,0x14400,0x14647,
    0x16800,0x16a39,0x16a40,0x16a5f,0x16ad0,0x16aee,0x16b00,0x16b30,0x16b40,0x16b44,0x16b63,0x16b78,0x16b7d,0x16b90,0x16e40,0x16e80,0x16f00,0x16f45,0x16f50,0x16f51,0x16f93,
    0x16fa0,0x16fe0,0x16fe2,0x17000,0x187f2,0x18800,0x18af3,0x1b000,0x1b11f,0x1b170,0x1b2fc,0x1bc00,0x1bc6b,0x1bc70,0x1bc7d,0x1bc80,0x1bc89,0x1bc90,0x1bc9a,0x1d400,0x1d455,
    0x1d456,0x1d49d,0x1d49e,0x1d4a0,0x1d4a2,0x1d4a3,0x1d4a5,0x1d4a7,0x1d4a9,0x1d4ad,0x1d4ae,0x1d4ba,0x1d4bb,0x1d4bc,0x1d4bd,0x1d4c4,0x1d4c5,0x1d506,0x1d507,0x1d50b,0x1d50d,
    0x1d515,0x1d516,0x1d51d,0x1d51e,0x1d53a,0x1d53b,0x1d53f,0x1d540,0x1d545,0x1d546,0x1d547,0x1d54a,0x1d551,0x1d552,0x1d6a6,0x1d6a8,0x1d6c1,0x1d6c2,0x1d6db,0x1d6dc,0x1d6fb,
    0x1d6fc,0x1d715,0x1d716,0x1d735,0x1d736,0x1d74f,0x1d750,0x1d76f,0x1d770,0x1d789,0x1d78a,0x1d7a9,0x1d7aa,0x1d7c3,0x1d7c4,0x1d7cc,0x1e800,0x1e8c5,0x1e900,0x1e944,0x1ee00,
    0x1ee04,0x1ee05,0x1ee20,0x1ee21,0x1ee23,0x1ee24,0x1ee25,0x1ee27,0x1ee28,0x1ee29,0x1ee33,0x1ee34,0x1ee38,0x1ee39,0x1ee3a,0x1ee3b,0x1ee3c,0x1ee42,0x1ee43,0x1ee47,0x1ee48,
    0x1ee49,0x1ee4a,0x1ee4b,0x1ee4c,0x1ee4d,0x1ee50,0x1ee51,0x1ee53,0x1ee54,0x1ee55,0x1ee57,0x1ee58,0x1ee59,0x1ee5a,0x1ee5b,0x1ee5c,0x1ee5d,0x1ee5e,0x1ee5f,0x1ee60,0x1ee61,
    0x1ee63,0x1ee64,0x1ee65,0x1ee67,0x1ee6b,0x1ee6c,0x1ee73,0x1ee74,0x1ee78,0x1ee79,0x1ee7d,0x1ee7e,0x1ee7f,0x1ee80,0x1ee8a,0x1ee8b,0x1ee9c,0x1eea1,0x1eea4,0x1eea5,0x1eeaa,
    0x1eeab,0x1eebc,0x20000,0x2a6d7,0x2a700,0x2b735,0x2b740,0x2b81e,0x2b820,0x2cea2,0x2ceb0,0x2ebe1,0x2f800,0x2fa1e,
};

const uint16_t UnicodeIdentifierTailLookup16[] =
{
    0x00aa,0x00ab,0x00b5,0x00b6,0x00ba,0x00bb,0x00c0,0x00d7,0x00d8,0x00f7,0x00f8,0x02c2,0x02c6,0x02d2,0x02e0,0x02e5,0x02ec,0x02ed,0x02ee,0x02ef,0x0300,0x0375,0x0376,0x0378,
    0x037a,0x037e,0x037f,0x0380,0x0386,0x0387,0x0388,0x038b,0x038c,0x038d,0x038e,0x03a2,0x03a3,0x03f6,0x03f7,0x0482,0x0483,0x0488,0x048a,0x0530,0x0531,0x0557,0x0559,0x055a,
    0x0560,0x0589,0x0591,0x05be,0x05bf,0x05c0,0x05c1,0x05c3,0x05c4,0x05c6,0x05c7,0x05c8,0x05d0,0x05eb,0x05ef,0x05f3,0x0610,0x061b,0x0620,0x066a,0x066e,0x06d4,0x06d5,0x06dd,
    0x06df,0x06e9,0x06ea,0x06fd,0x06ff,0x0700,0x0710,0x074b,0x074d,0x07b2,0x07c0,0x07f6,0x07fa,0x07fb,0x07fd,0x07fe,0x0800,0x082e,0x0840,0x085c,0x0860,0x086b,0x08a0,0x08b5,
    0x08b6,0x08be,0x08d3,0x08e2,0x08e3,0x0964,0x0966,0x0970,0x0971,0x0984,0x0985,0x098d,0x098f,0x0991,0x0993,0x09a9,0x09aa,0x09b1,0x09b2,0x09b3,0x09b6,0x09ba,0x09bc,0x09c5,
    0x09c7,0x09c9,0x09cb,0x09cf,0x09d7,0x09d8,0x09dc,0x09de,0x09df,0x09e4,0x09e6,0x09f2,0x09fc,0x09fd,0x09fe,0x09ff,0x0a01,0x0a04,0x0a05,0x0a0b,0x0a0f,0x0a11,0x0a13,0x0a29,
    0x0a2a,0x0a31,0x0a32,0x0a34,0x0a35,0x0a37,0x0a38,0x0a3a,0x0a3c,0x0a3d,0x0a3e,0x0a43,0x0a47,0x0a49,0x0a4b,0x0a4e,0x0a51,0x0a52,0x0a59,0x0a5d,0x0a5e,0x0a5f,0x0a66,0x0a76,
    0x0a81,0x0a84,0x0a85,0x0a8e,0x0a8f,0x0a92,0x0a93,0x0aa9,0x0aaa,0x0ab1,0x0ab2,0x0ab4,0x0ab5,0x0aba,0x0abc,0x0ac6,0x0ac7,0x0aca,0x0acb,0x0ace,0x0ad0,0x0ad1,0x0ae0,0x0ae4,
    0x0ae6,0x0af0,0x0af9,0x0b00,0x0b01,0x0b04,0x0b05,0x0b0d,0x0b0f,0x0b11,0x0b13,0x0b29,0x0b2a,0x0b31,0x0b32,0x0b34,0x0b35,0x0b3a,0x0b3c,0x0b45,0x0b47,0x0b49,0x0b4b,0x0b4e,
    0x0b56,0x0b58,0x0b5c,0x0b5e,0x0b5f,0x0b64,0x0b66,0x0b70,0x0b71,0x0b72,0x0b82,0x0b84,0x0b85,0x0b8b,0x0b8e,0x0b91,0x0b92,0x0b96,0x0b99,0x0b9b,0x0b9c,0x0b9d,0x0b9e,0x0ba0,
    0x0ba3,0x0ba5,0x0ba8,0x0bab,0x0bae,0x0bba,0x0bbe,0x0bc3,0x0bc6,0x0bc9,0x0bca,0x0bce,0x0bd0,0x0bd1,0x0bd7,0x0bd8,0x0be6,0x0bf0,0x0c00,0x0c0d,0x0c0e,0x0c11,0x0c12,0x0c29,
    0x0c2a,0x0c3a,0x0c3d,0x0c45,0x0c46,0x0c49,0x0c4a,0x0c4e,0x0c55,0x0c57,0x0c58,0x0c5b,0x0c60,0x0c64,0x0c66,0x0c70,0x0c80,0x0c84,0x0c85,0x0c8d,0x0c8e,0x0c91,0x0c92,0x0ca9,
    0x0caa,0x0cb4,0x0cb5,0x0cba,0x0cbc,0x0cc5,0x0cc6,0x0cc9,0x0cca,0x0cce,0x0cd5,0x0cd7,0x0cde,0x0cdf,0x0ce0,0x0ce4,0x0ce6,0x0cf0,0x0cf1,0x0cf3,0x0d00,0x0d04,0x0d05,0x0d0d,
    0x0d0e,0x0d11,0x0d12,0x0d45,0x0d46,0x0d49,0x0d4a,0x0d4f,0x0d54,0x0d58,0x0d5f,0x0d64,0x0d66,0x0d70,0x0d7a,0x0d80,0x0d82,0x0d84,0x0d85,0x0d97,0x0d9a,0x0db2,0x0db3,0x0dbc,
    0x0dbd,0x0dbe,0x0dc0,0x0dc7,0x0dca,0x0dcb,0x0dcf,0x0dd5,0x0dd6,0x0dd7,0x0dd8,0x0de0,0x0de6,0x0df0,0x0df2,0x0df4,0x0e01,0x0e3b,0x0e40,0x0e4f,0x0e50,0x0e5a,0x0e81,0x0e83,
    0x0e84,0x0e85,0x0e87,0x0e89,0x0e8a,0x0e8b,0x0e8d,0x0e8e,0x0e94,0x0e98,0x0e99,0x0ea0,0x0ea1,0x0ea4,0x0ea5,0x0ea6,0x0ea7,0x0ea8,0x0eaa,0x0eac,0x0ead,0x0eba,0x0ebb,0x0ebe,
    0x0ec0,0x0ec5,0x0ec6,0x0ec7,0x0ec8,0x0ece,0x0ed0,0x0eda,0x0edc,0x0ee0,0x0f00,0x0f01,0x0f18,0x0f1a,0x0f20,0x0f2a,0x0f35,0x0f36,0x0f37,0x0f38,0x0f39,0x0f3a,0x0f3e,0x0f48,
    0x0f49,0x0f6d,0x0f71,0x0f85,0x0f86,0x0f98,0x0f99,0x0fbd,0x0fc6,0x0fc7,0x1000,0x104a,0x1050,0x109e,0x10a0,0x10c6,0x10c7,0x10c8,0x10cd,0x10ce,0x10d0,0x10fb,0x10fc,0x1249,
    0x124a,0x124e,0x1250,0x1257,0x1258,0x1259,0x125a,0x125e,0x1260,0x1289,0x128a,0x128e,0x1290,0x12b1,0x12b2,0x12b6,0x12b8,0x12bf,0x12c0,0x12c1,0x12c2,0x12c6,0x12c8,0x12d7,
    0x12d8,0x1311,0x1312,0x1316,0x1318,0x135b,0x135d,0x1360,0x1380,0x1390,0x13a0,0x13f6,0x13f8,0x13fe,0x1401,0x166d,0x166f,0x1680,0x1681,0x169b,0x16a0,0x16eb,0x16ee,0x16f9,
    0x1700,0x170d,0x170e,0x1715,0x1720,0x1735,0x1740,0x1754,0x1760,0x176d,0x176e,0x1771,0x1772,0x1774,0x1780,0x17d4,0x17d7,0x17d8,0x17dc,0x17de,0x17e0,0x17ea,0x180b,0x180e,
    0x1810,0x181a,0x1820,0x1879,0x1880,0x18ab,0x18b0,0x18f6,0x1900,0x191f,0x1920,0x192c,0x1930,0x193c,0x1946,0x196e,0x1970,0x1975,0x1980,0x19ac,0x19b0,0x19ca,0x19d0,0x19da,
    0x1a00,0x1a1c,0x1a20,0x1a5f,0x1a60,0x1a7d,0x1a7f,0x1a8a,0x1a90,0x1a9a,0x1aa7,0x1aa8,0x1ab0,0x1abe,0x1b00,0x1b4c,0x1b50,0x1b5a,0x1b6b,0x1b74,0x1b80,0x1bf4,0x1c00,0x1c38,
    0x1c40,0x1c4a,0x1c4d,0x1c7e,0x1c80,0x1c89,0x1c90,0x1cbb,0x1cbd,0x1cc0,0x1cd0,0x1cd3,0x1cd4,0x1cfa,0x1d00,0x1dfa,0x1dfb,0x1f16,0x1f18,0x1f1e,0x1f20,0x1f46,0x1f48,0x1f4e,
    0x1f50,0x1f58,0x1f59,0x1f5a,0x1f5b,0x1f5c,0x1f5d,0x1f5e,0x1f5f,0x1f7e,0x1f80,0x1fb5,0x1fb6,0x1fbd,0x1fbe,0x1fbf,0x1fc2,0x1fc5,0x1fc6,0x1fcd,0x1fd0,0x1fd4,0x1fd6,0x1fdc,
    0x1fe0,0x1fed,0x1ff2,0x1ff5,0x1ff6,0x1ffd,0x200c,0x200e,0x203f,0x2041,0x2054,0x2055,0x2071,0x2072,0x207f,0x2080,0x2090,0x209d,0x20d0,0x20dd,0x20e1,0x20e2,0x20e5,0x20f1,
    0x2102,0x2103,0x2107,0x2108,0x210a,0x2114,0x2115,0x2116,0x2119,0x211e,0x2124,0x2125,0x2126,0x2127,0x2128,0x2129,0x212a,0x212e,0x212f,0x213a,0x213c,0x2140,0x2145,0x214a,
    0x214e,0x214f,0x2160,0x2189,0x2c00,0x2c2f,0x2c30,0x2c5f,0x2c60,0x2ce5,0x2ceb,0x2cf4,0x2d00,0x2d26,0x2d27,0x2d28,0x2d2d,0x2d2e,0x2d30,0x2d68,0x2d6f,0x2d70,0x2d7f,0x2d97,
    0x2da0,0x2da7,0x2da8,0x2daf,0x2db0,0x2db7,0x2db8,0x2dbf,0x2dc0,0x2dc7,0x2dc8,0x2dcf,0x2dd0,0x2dd7,0x2dd8,0x2ddf,0x2de0,0x2e00,0x2e2f,0x2e30,0x3005,0x3008,0x3021,0x3030,
    0x3031,0x3036,0x3038,0x303d,0x3041,0x3097,0x3099,0x309b,0x309d,0x30a0,0x30a1,0x30fb,0x30fc,0x3100,0x3105,0x3130,0x3131,0x318f,0x31a0,0x31bb,0x31f0,0x3200,0x3400,0x4db6,
    0x4e00,0x9ff0,0xa000,0xa48d,0xa4d0,0xa4fe,0xa500,0xa60d,0xa610,0xa62c,0xa640,0xa670,0xa674,0xa67e,0xa67f,0xa6f2,0xa717,0xa720,0xa722,0xa789,0xa78b,0xa7ba,0xa7f7,0xa828,
    0xa840,0xa874,0xa880,0xa8c6,0xa8d0,0xa8da,0xa8e0,0xa8f8,0xa8fb,0xa8fc,0xa8fd,0xa92e,0xa930,0xa954,0xa960,0xa97d,0xa980,0xa9c1,0xa9cf,0xa9da,0xa9e0,0xa9ff,0xaa00,0xaa37,
    0xaa40,0xaa4e,0xaa50,0xaa5a,0xaa60,0xaa77,0xaa7a,0xaac3,0xaadb,0xaade,0xaae0,0xaaf0,0xaaf2,0xaaf7,0xab01,0xab07,0xab09,0xab0f,0xab11,0xab17,0xab20,0xab27,0xab28,0xab2f,
    0xab30,0xab5b,0xab5c,0xab66,0xab70,0xabeb,0xabec,0xabee,0xabf0,0xabfa,0xac00,0xd7a4,0xd7b0,0xd7c7,0xd7cb,0xd7fc,0xf900,0xfa6e,0xfa70,0xfada,0xfb00,0xfb07,0xfb13,0xfb18,
    0xfb1d,0xfb29,0xfb2a,0xfb37,0xfb38,0xfb3d,0xfb3e,0xfb3f,0xfb40,0xfb42,0xfb43,0xfb45,0xfb46,0xfbb2,0xfbd3,0xfd3e,0xfd50,0xfd90,0xfd92,0xfdc8,0xfdf0,0xfdfc,0xfe00,0xfe10,
    0xfe20,0xfe30,0xfe33,0xfe35,0xfe4d,0xfe50,0xfe70,0xfe75,0xfe76,0xfefd,0xff10,0xff1a,0xff21,0xff3b,0xff3f,0xff40,0xff41,0xff5b,0xff66,0xffbf,0xffc2,0xffc8,0xffca,0xffd0,
    0xffd2,0xffd8,0xffda,0xffdd,
};

const uint32_t UnicodeIdentifierTailLookup32[] =
{
    0x10000,0x1000c,0x1000d,0x10027,0x10028,0x1003b,0x1003c,0x1003e,0x1003f,0x1004e,0x10050,0x1005e,0x10080,0x100fb,0x10140,0x10175,0x101fd,0x101fe,0x10280,0x1029d,0x102a0,
    0x102d1,0x102e0,0x102e1,0x10300,0x10320,0x1032d,0x1034b,0x10350,0x1037b,0x10380,0x1039e,0x103a0,0x103c4,0x103c8,0x103d0,0x103d1,0x103d6,0x10400,0x1049e,0x104a0,0x104aa,
    0x104b0,0x104d4,0x104d8,0x104fc,0x10500,0x10528,0x10530,0x10564,0x10600,0x10737,0x10740,0x10756,0x10760,0x10768,0x10800,0x10806,0x10808,0x10809,0x1080a,0x10836,0x10837,
    0x10839,0x1083c,0x1083d,0x1083f,0x10856,0x10860,0x10877,0x10880,0x1089f,0x108e0,0x108f3,0x108f4,0x108f6,0x10900,0x10916,0x10920,0x1093a,0x10980,0x109b8,0x109be,0x109c0,
    0x10a00,0x10a04,0x10a05,0x10a07,0x10a0c,0x10a14,0x10a15,0x10a18,0x10a19,0x10a36,0x10a38,0x10a3b,0x10a3f,0x10a40,0x10a60,0x10a7d,0x10a80,0x10a9d,0x10ac0,0x10ac8,0x10ac9,
    0x10ae7,0x10b00,0x10b36,0x10b40,0x10b56,0x10b60,0x10b73,0x10b80,0x10b92,0x10c00,0x10c49,0x10c80,0x10cb3,0x10cc0,0x10cf3,0x10d00,0x10d28,0x10d30,0x10d3a,0x10f00,0x10f1d,
    0x10f27,0x10f28,0x10f30,0x10f51,0x11000,0x11047,0x11066,0x11070,0x1107f,0x110bb,0x110d0,0x110e9,0x110f0,0x110fa,0x11100,0x11135,0x11136,0x11140,0x11144,0x11147,0x11150,
    0x11174,0x11176,0x11177,0x11180,0x111c5,0x111c9,0x111cd,0x111d0,0x111db,0x111dc,0x111dd,0x11200,0x11212,0x11213,0x11238,0x1123e,0x1123f,0x11280,0x11287,0x11288,0x11289,
    0x1128a,0x1128e,0x1128f,0x1129e,0x1129f,0x112a9,0x112b0,0x112eb,0x112f0,0x112fa,0x11300,0x11304,0x11305,0x1130d,0x1130f,0x11311,0x11313,0x11329,0x1132a,0x11331,0x11332,
    0x11334,0x11335,0x1133a,0x1133b,0x11345,0x11347,0x11349,0x1134b,0x1134e,0x11350,0x11351,0x11357,0x11358,0x1135d,0x11364,0x11366,0x1136d,0x11370,0x11375,0x11400,0x1144b,
    0x11450,0x1145a,0x1145e,0x1145f,0x11480,0x114c6,0x114c7,0x114c8,0x114d0,0x114da,0x11580,0x115b6,0x115b8,0x115c1,0x115d8,0x115de,0x11600,0x11641,0x11644,0x11645,0x11650,
    0x1165a,0x11680,0x116b8,0x116c0,0x116ca,0x11700,0x1171b,0x1171d,0x1172c,0x11730,0x1173a,0x11800,0x1183b,0x118a0,0x118ea,0x118ff,0x11900,0x11a00,0x11a3f,0x11a47,0x11a48,
    0x11a50,0x11a84,0x11a86,0x11a9a,0x11a9d,0x11a9e,0x11ac0,0x11af9,0x11c00,0x11c09,0x11c0a,0x11c37,0x11c38,0x11c41,0x11c50,0x11c5a,0x11c72,0x11c90,0x11c92,0x11ca8,0x11ca9,
    0x11cb7,0x11d00,0x11d07,0x11d08,0x11d0a,0x11d0b,0x11d37,0x11d3a,0x11d3b,0x11d3c,0x11d3e,0x11d3f,0x11d48,0x11d50,0x11d5a,0x11d60,0x11d66,0x11d67,0x11d69,0x11d6a,0x11d8f,
    0x11d90,0x11d92,0x11d93,0x11d99,0x11da0,0x11daa,0x11ee0,0x11ef7,0x12000,0x1239a,0x12400,0x1246f,0x12480,0x12544,0x13000,0x1342f,0x14400,0x14647,0x16800,0x16a39,0x16a40,
    0x16a5f,0x16a60,0x16a6a,0x16ad0,0x16aee,0x16af0,0x16af5,0x16b00,0x16b37,0x16b40,0x16b44,0x16b50,0x16b5a,0x16b63,0x16b78,0x16b7d,0x16b90,0x16e40,0x16e80,0x16f00,0x16f45,
    0x16f50,0x16f7f,0x16f8f,0x16fa0,0x16fe0,0x16fe2,0x17000,0x187f2,0x18800,0x18af3,0x1b000,0x1b11f,0x1b170,0x1b2fc,0x1bc00,0x1bc6b,0x1bc70,0x1bc7d,0x1bc80,0x1bc89,0x1bc90,
    0x1bc9a,0x1bc9d,0x1bc9f,0x1d165,0x1d16a,0x1d16d,0x1d173,0x1d17b,0x1d183,0x1d185,0x1d18c,0x1d1aa,0x1d1ae,0x1d242,0x1d245,0x1d400,0x1d455,0x1d456,0x1d49d,0x1d49e,0x1d4a0,
    0x1d4a2,0x1d4a3,0x1d4a5,0x1d4a7,0x1d4a9,0x1d4ad,0x1d4ae,0x1d4ba,0x1d4bb,0x1d4bc,0x1d4bd,0x1d4c4,0x1d4c5,0x1d506,0x1d507,0x1d50b,0x1d50d,0x1d515,0x1d516,0x1d51d,0x1d51e,
    0x1d53a,0x1d53b,0x1d53f,0x1d540,0x1d545,0x1d546,0x1d547,0x1d54a,0x1d551,0x1d552,0x1d6a6,0x1d6a8,0x1d6c1,0x1d6c2,0x1d6db,0x1d6dc,0x1d6fb,0x1d6fc,0x1d715,0x1d716,0x1d735,
    0x1d736,0x1d74f,0x1d750,0x1d76f,0x1d770,0x1d789,0x1d78a,0x1d7a9,0x1d7aa,0x1d7c3,0x1d7c4,0x1d7cc,0x1d7ce,0x1d800,0x1da00,0x1da37,0x1da3b,0x1da6d,0x1da75,0x1da76,0x1da84,
    0x1da85,0x1da9b,0x1daa0,0x1daa1,0x1dab0,0x1e000,0x1e007,0x1e008,0x1e019,0x1e01b,0x1e022,0x1e023,0x1e025,0x1e026,0x1e02b,0x1e800,0x1e8c5,0x1e8d0,0x1e8d7,0x1e900,0x1e94b,
    0x1e950,0x1e95a,0x1ee00,0x1ee04,0x1ee05,0x1ee20,0x1ee21,0x1ee23,0x1ee24,0x1ee25,0x1ee27,0x1ee28,0x1ee29,0x1ee33,0x1ee34,0x1ee38,0x1ee39,0x1ee3a,0x1ee3b,0x1ee3c,0x1ee42,
    0x1ee43,0x1ee47,0x1ee48,0x1ee49,0x1ee4a,0x1ee4b,0x1ee4c,0x1ee4d,0x1ee50,0x1ee51,0x1ee53,0x1ee54,0x1ee55,0x1ee57,0x1ee58,0x1ee59,0x1ee5a,0x1ee5b,0x1ee5c,0x1ee5d,0x1ee5e,
    0x1ee5f,0x1ee60,0x1ee61,0x1ee63,0x1ee64,0x1ee65,0x1ee67,0x1ee6b,0x1ee6c,0x1ee73,0x1ee74,0x1ee78,0x1ee79,0x1ee7d,0x1ee7e,0x1ee7f,0x1ee80,0x1ee8a,0x1ee8b,0x1ee9c,0x1eea1,
    0x1eea4,0x1eea5,0x1eeaa,0x1eeab,0x1eebc,0x20000,0x2a6d7,0x2a700,0x2b735,0x2b740,0x2b81e,0x2b820,0x2cea2,0x2ceb0,0x2ebe1,0x2f800,0x2fa1e,0xe0100,0xe01f0,
};

}

namespace uLang
{

const bool CUnicode::_ASCIITable_Whitespace[BYTE_RANGE] =
{   // _0    _1    _2    _3    _4    _5    _6    _7    _8    _9    _a    _b    _c    _d    _e    _f
    false,false,false,false,false,false,false,false,false, true, true, true, true, true,false,false,  // 0_
    false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,  // 1_
     true,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,  // 2_
    false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,  // 3_
    false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,  // 4_
    false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,  // 5_
    false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,  // 6_
    false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,  // 7_
    // Non-ASCII
    false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,
    false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,
    false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,
    false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,
    false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,
    false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,
    false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,
    false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,
};

const bool CUnicode::_ASCIITable_Identifier[BYTE_RANGE] =
{   // _0    _1    _2    _3    _4    _5    _6    _7    _8    _9    _a    _b    _c    _d    _e    _f
    false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,  // 0_
    false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,  // 1_
    false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,  // 2_
     true, true, true, true, true, true, true, true, true, true,false,false,false,false,false,false,  // 3_
    false, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true,  // 4_
     true, true, true, true, true, true, true, true, true, true, true,false,false,false,false, true,  // 5_
    false, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true,  // 6_
     true, true, true, true, true, true, true, true, true, true, true,false,false,false,false,false,  // 7_
    // Non-ASCII
    false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,
    false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,
    false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,
    false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,
    false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,
    false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,
    false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,
    false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,
};

SUTF8CodePoint CUnicode::EncodeUTF8(UniCodePoint CodePoint)
{
    SUTF8CodePoint Result;
    if (CodePoint < 0x80)
    {
        // One byte encoding: 0xxxxxxx
        Result.NumUnits = 1;
        Result.Units[0] = UTF8Char(CodePoint);
    }
    else if (CodePoint < 0x800)
    {
        // Two byte encoding: 110xxxxx 10xxxxxx
        Result.NumUnits = 2;
        Result.Units[0] = UTF8Char(((CodePoint >> 6) & 0x1F) | 0xC0);
        Result.Units[1] = UTF8Char(((CodePoint     ) & 0x3F) | 0x80);
    }
    else if (CodePoint < 0x10000)
    {
        // Three byte encoding: 1110xxxx 10xxxxxx 10xxxxxx
        Result.NumUnits = 3;
        Result.Units[0] = UTF8Char(((CodePoint >> 12) & 0x0F) | 0xE0);
        Result.Units[1] = UTF8Char(((CodePoint >>  6) & 0x3F) | 0x80);
        Result.Units[2] = UTF8Char(((CodePoint      ) & 0x3F) | 0x80);
    }
    else if (CodePoint < 0x200000)
    {
        // Four byte encoding: 11110xxx 10xxxxxx 10xxxxxx 10xxxxxx
        Result.NumUnits = 4;
        Result.Units[0] = UTF8Char(((CodePoint >> 18) & 0x07) | 0xF0);
        Result.Units[1] = UTF8Char(((CodePoint >> 12) & 0x3F) | 0x80);
        Result.Units[2] = UTF8Char(((CodePoint >>  6) & 0x3F) | 0x80);
        Result.Units[3] = UTF8Char(((CodePoint      ) & 0x3F) | 0x80);
    }
    else
    {
        Result.NumUnits = 0;
    }
    return Result;
}

SUniCodePointLength CUnicode::DecodeUTF8NonASCII(const UTF8Char * Text, size_t TextByteLength)
{
    ULANG_ASSERTF(TextByteLength > 0 && *Text >= ASCII_RANGE, "DecodeUTF8NonASCII() was called with empty string or ASCII code point.");

    // https://en.wikipedia.org/wiki/Specials_(Unicode_block)#Replacement_character
    constexpr UniCodePoint BogusCodePoint                   = 0xFFFD;  // �
    constexpr UniCodePoint UTF16HighSurrogateStartCodePoint = 0xD800;
    constexpr UniCodePoint UTF16HighSurrogateEndCodePoint   = 0xDBFF;
    constexpr UniCodePoint UTF16LowSurrogateStartCodePoint  = 0xDC00;
    constexpr UniCodePoint UTF16LowSurrogateEndCodePoint    = 0xDFFF;

    UniCodePoint Byte1 = *Text;
    UniCodePoint Byte2, Byte3, Byte4;

    // If Byte1 < 192, fall through and return BogusCodePoint
    if (Byte1 >= 192 && Byte1 < 224)  // two bytes
    {
        // Ensure our string has enough characters to read from
        if (TextByteLength < 2)
        {
            return { BogusCodePoint, uint8_t(TextByteLength) }; // Skip to end and write out a single code point
        }

        Byte1 -= (128 + 64);
        Byte2 = *++Text;
        if ((Byte2 & (128 + 64)) == 128)  // Verify format 10xxxxxx
        {
            UniCodePoint CodePoint = ((Byte1 << 6) | (Byte2 - 128));
            if ((CodePoint >= 0x80) && (CodePoint <= 0x7FF))
            {
                return { CodePoint, 2 };  // Success: skip to next possible start of codepoint.
            }
        }
    }
    else if (Byte1 < 240)  // three bytes
    {
        // Ensure our string has enough characters to read from
        if (TextByteLength < 3)
        {
            return { BogusCodePoint, uint8_t(TextByteLength) }; // Skip to end and write out a single code point
        }

        Byte1 -= (128 + 64 + 32);
        Byte2 = *++Text;
        Byte3 = *++Text;
        if ((Byte2 & (128 + 64)) == 128 && (Byte3 & (128 + 64)) == 128) // Verify format 10xxxxxx
        {
            UniCodePoint CodePoint = (((Byte1 << 12)) | ((Byte2 - 128) << 6) | ((Byte3 - 128)));

            // UTF-8 characters cannot be in the UTF-16 surrogates range
            // 0xFFFE and 0xFFFF are illegal, too, so we check them at the edge.
            if ((CodePoint < UTF16HighSurrogateStartCodePoint || CodePoint > UTF16HighSurrogateEndCodePoint)
             && (CodePoint < UTF16LowSurrogateStartCodePoint || CodePoint > UTF16LowSurrogateEndCodePoint)
             && (CodePoint >= 0x800 && CodePoint <= 0xFFFD))
            {
                return { CodePoint, 3 }; // Success: skip to next possible start of codepoint.
            }
        }
    }
    else if (Byte1 < 248)  // four bytes
    {
        // Ensure our string has enough characters to read from
        if (TextByteLength < 4)
        {
            return { BogusCodePoint, uint8_t(TextByteLength) }; // Skip to end and write out a single code point
        }

        Byte1 -= (128 + 64 + 32 + 16);
        Byte2 = *++Text;
        Byte3 = *++Text;
        Byte4 = *++Text;
        if ((Byte2 & (128 + 64)) == 128 && (Byte3 & (128 + 64)) == 128 && (Byte4 & (128 + 64)) == 128) // Verify format 10xxxxxx
        {
            UniCodePoint CodePoint = (((Byte1 << 18)) | ((Byte2 - 128) << 12) | ((Byte3 - 128) << 6) | ((Byte4 - 128)));
            if ((CodePoint >= 0x10000) && (CodePoint <= 0x10FFFF))
            {
                return { CodePoint, 4 }; // Success: skip to next possible start of codepoint.
            }
        }
    }
    // Five and six octet sequences became illegal in rfc3629.
    //  We throw the codepoint away, but parse them to make sure we move
    //  ahead the right number of bytes and don't overflow the buffer.
    else if (Byte1 < 252)  // five bytes
    {
        // Ensure our string has enough characters to read from
        if (TextByteLength < 5)
        {
            return { BogusCodePoint, uint8_t(TextByteLength) }; // Skip to end and write out a single code point
        }

        if ((*++Text & (128 + 64)) == 128
         && (*++Text & (128 + 64)) == 128
         && (*++Text & (128 + 64)) == 128
         && (*++Text & (128 + 64)) == 128)
        {
            return { BogusCodePoint, 5 }; // skip to next possible start of codepoint.
        }
    }
    else  // six bytes
    {
        // Ensure our string has enough characters to read from
        if (TextByteLength < 6)
        {
            return { BogusCodePoint, uint8_t(TextByteLength) }; // Skip to end and write out a single code point
        }

        if ((*++Text & (128 + 64)) == 128
         && (*++Text & (128 + 64)) == 128
         && (*++Text & (128 + 64)) == 128
         && (*++Text & (128 + 64)) == 128
         && (*++Text & (128 + 64)) == 128)
        {
            return { BogusCodePoint, 6 }; // skip to next possible start of codepoint.
        }
    }

    // Catch everything else
    return { BogusCodePoint, 1 }; // Sequence was not valid UTF-8. Skip the first byte and continue.
}

bool CUnicode::IsIdentifierStartNonASCII(UniCodePoint CodePoint)
{
    ULANG_ASSERTF(CodePoint >= ASCII_RANGE, "IsIdentifierStartNonASCII() was called with ASCII code point.");

    // If the lower bound has an even index in the lookup table, the code point is a match
    return (CodePoint < 0x10000
        ? (FindLowerBound(UnicodeIdentifierStartLookup16, ULANG_COUNTOF(UnicodeIdentifierStartLookup16), CodePoint) & 1) == 0
        : (FindLowerBound(UnicodeIdentifierStartLookup32, ULANG_COUNTOF(UnicodeIdentifierStartLookup32), CodePoint) & 1) == 0);
}

bool CUnicode::IsIdentifierTailNonASCII(UniCodePoint CodePoint)
{
    ULANG_ASSERTF(CodePoint >= ASCII_RANGE, "IsIdentifierTailNonASCII() was called with ASCII code point.");

    // If the lower bound has an even index in the lookup table, the code point is a match
    return (CodePoint < 0x10000
        ? (FindLowerBound(UnicodeIdentifierTailLookup16, ULANG_COUNTOF(UnicodeIdentifierTailLookup16), CodePoint) & 1) == 0
        : (FindLowerBound(UnicodeIdentifierTailLookup32, ULANG_COUNTOF(UnicodeIdentifierTailLookup32), CodePoint) & 1) == 0);
}

}
