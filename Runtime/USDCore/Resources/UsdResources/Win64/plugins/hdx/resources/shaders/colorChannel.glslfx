-- glslfx version 0.1

//
// Copyright 2019 Pixar
//
// Licensed under the terms set forth in the LICENSE.txt file available at
// https://openusd.org/license.
//

-- configuration
{
    "techniques": {
        "default": {
            "ColorChannelFragment": {
                "source": [ "ColorChannel.Fragment" ]
            }
        }
    }
}

-- glsl ColorChannel.Fragment


// Display channel values. These should match the the indices of the entries in
// HdxDisplayChannelTokens
#define CHANNEL_COLOR     0
#define CHANNEL_RED       1
#define CHANNEL_GREEN     2
#define CHANNEL_BLUE      3
#define CHANNEL_ALPHA     4
#define CHANNEL_LUMINANCE 5

void main(void)
{
    vec2 fragCoord = uvOut * screenSize;
    vec4 color = HgiTexelFetch_colorIn(ivec2(fragCoord));

    // Display Channel
    if (channel == CHANNEL_RED) {
        color.g = color.b = color.r;
    } else if (channel == CHANNEL_GREEN) {
        color.r = color.b = color.g;
    } else if (channel == CHANNEL_BLUE) {
        color.r = color.g = color.b;
    } else if (channel == CHANNEL_ALPHA) {
        color.r = color.g = color.b = color.a;
    } else if (channel == CHANNEL_LUMINANCE) {
        const vec3 W = vec3(0.30, 0.59, 0.11);
        color.r = color.g = color.b = dot(color.rgb, W);
    } // Do nothing if channel == CHANNEL_COLOR

    hd_FragColor = color;
}
