<?xml version="1.0" encoding="utf-8"?>
<!--MobileFSR plugin additions-->
<root xmlns:android="http://schemas.android.com/apk/res/android">
	<!-- init section is always evaluated once per architecture -->
	<init>
		<log text="MobileFSR init"/>
		<setBool result="bSupported" value="false"/>
		<isArch arch="armeabi-v7a">
			<setBool result="bSupported" value="true"/>
		</isArch>
		<isArch arch="arm64-v8a">
			<setBool result="bSupported" value="true"/>
		</isArch>
	</init>
</root>
