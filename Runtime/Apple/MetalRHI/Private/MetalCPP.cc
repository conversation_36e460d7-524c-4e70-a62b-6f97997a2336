// Copyright Epic Games, Inc. All Rights Reserved.

/*=============================================================================
    MetalCPP.cc: MetalCPP Definitions
=============================================================================*/

// This cpp file adds the implementations required for MetalCPP
#define NS_PRIVATE_IMPLEMENTATION
#define MTL_PRIVATE_IMPLEMENTATION
#define CA_PRIVATE_IMPLEMENTATION

#include <Foundation/Foundation.hpp>
#include <Metal/Metal.hpp>
#include <QuartzCore/QuartzCore.hpp>
