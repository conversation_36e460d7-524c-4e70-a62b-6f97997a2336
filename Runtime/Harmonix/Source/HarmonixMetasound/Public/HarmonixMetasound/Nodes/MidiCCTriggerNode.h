// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "MetasoundNodeInterface.h"
#include "HarmonixMetasound/Common.h"

namespace HarmonixMetasound::Nodes::MidiCCTriggerNode
{
	const HARMONIXMETASOUND_API Metasound::FNode<PERSON><PERSON>Name& GetClassName();
	HARMONIXMETASOUND_API int32 GetCurrentMajorVersion();
	
	namespace Inputs
	{
		DECLARE_METASOUND_PARAM_ALIAS(Enable);
		DECLARE_METASOUND_PARAM_ALIAS(MidiStream);
		DECLARE_METASOUND_PARAM_EXTERN(InputMidiControllerID);
	}

	namespace Outputs
	{
		DECLARE_METASOUND_PARAM_EXTERN(OutputControlChangeValueInt32);
	}
}